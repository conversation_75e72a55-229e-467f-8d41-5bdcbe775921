const RNFS = {
  DocumentDirectoryPath: '/mock/documents',
  TemporaryDirectoryPath: '/mock/temp',
  CachesDirectoryPath: '/mock/caches',
  ExternalDirectoryPath: '/mock/external',
  ExternalStorageDirectoryPath: '/mock/external-storage',
  DownloadDirectoryPath: '/mock/downloads',
  PicturesDirectoryPath: '/mock/pictures',
  MoviesDirectoryPath: '/mock/movies',
  MusicDirectoryPath: '/mock/music',
  LibraryDirectoryPath: '/mock/library',
  MainBundlePath: '/mock/bundle',

  // Mock methods
  readFile: jest.fn(() => Promise.resolve('')),
  writeFile: jest.fn(() => Promise.resolve()),
  exists: jest.fn(() => Promise.resolve(true)),
  mkdir: jest.fn(() => Promise.resolve()),
  unlink: jest.fn(() => Promise.resolve()),
  readDir: jest.fn(() => Promise.resolve([])),
  stat: jest.fn(() => Promise.resolve({})),
  copyFile: jest.fn(() => Promise.resolve()),
  moveFile: jest.fn(() => Promise.resolve()),
  downloadFile: jest.fn(() => Promise.resolve({})),
  uploadFiles: jest.fn(() => Promise.resolve({})),

  // Legacy properties for compatibility
  DocumentDir: '/mock/documents',
  CacheDir: '/mock/caches',
  ExternalCacheDir: '/mock/external-cache',
  ExternalStorageDir: '/mock/external-storage',
};

export default RNFS;
