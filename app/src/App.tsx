// Analytics
import { analyticsEvent, AnalyticsMiddleware } from '@analytics';
// Outages
import { OutageAnalyticsEventOpenFullOutageScreen } from '@analytics/events/Outage/OpenFullOutageScreen';
// imported separately to avoid parsing issues when using other providers in tests
import { WalletLinkingConfig } from '@bp/bppay-wallet-feature';
import { useOnboarding } from '@bp/onboarding-mfe';
import { ScreenName, TagStatus } from '@bp/partnerdriver-mfe/dist/common/enums';
import {
  LanguageProvider,
  ProfileLinkingConfig,
  useAppSettings,
} from '@bp/profile-mfe';
import { UserTypes } from '@bp/profile-mfe/dist/common/enums';
import { RFIDLinkingConfig } from '@bp/rfid-mfe';
import { SupportedCountries, SupportedPartner } from '@common/enums';
import {
  ApolloGatewayProvider,
  AuthProvider,
  ChargeStateProvider,
  ConfigProvider,
  ConnectivityProvider,
  FavouritesProvider,
  GuestPaymentProvider,
  OnboardingProvider,
  OutageProvider,
  ThemeProvider,
  WalletProvider,
} from '@providers';
import { MigrationContextProvider } from '@providers/MigrationContextProvider';
import { useOutage } from '@providers/OutageProvider';
import ProfileAppProvider from '@providers/ProfileAppProvider';
import { ReturnNavigationProvider } from '@providers/ReturnNavigationProvider';
import { LinkingOptions, NavigationContainer } from '@react-navigation/native';
import Screens from '@screens';
import Outage from '@screens/Outage/Outage';
import { getRfidError } from '@utils/get-rfid-error';
import {
  applyBackHandleListener,
  navigate,
  navigation,
  removeBackHandleListener,
} from '@utils/navigation';
import { wait } from '@utils/wait';
import React, { useCallback, useEffect } from 'react';
import { Linking, LogBox } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { enableScreens } from 'react-native-screens';

import * as S from './App.styles';
// Launch Handler
import { LaunchHandler } from './components/LaunchHandler/LaunchHandler';
import { EmailUpdateMiddleware } from './middleware/EmailUpdateMiddleware';
import { OffersMiddleware } from './middleware/OffersMiddleware';
import { PausedSubscriptionMiddleware } from './middleware/PausedSubscriptionMiddleware';
import { UberPartnerMiddleware } from './middleware/UberPartnerMiddleware';
import { UberProMiddleware } from './middleware/UberProMiddleware';
import { default as HostAppTranslationsProvider } from './providers/HostAppTranslationsProvider';

// Optimize memory usage and performance by bringing the native navigation component (UIViewController for iOS, and FragmentActivity for Android)
enableScreens();

// Linking configuration
const linking: LinkingOptions<any> = {
  prefixes: ['bppaywallet://', 'bppulse://', 'chargemaster://'],
  config: {
    screens: {
      Wallet: WalletLinkingConfig,
      Tabs: {
        screens: {
          Map: 'map',
          Profile: ProfileLinkingConfig,
        },
      },
      Credit: 'credit_topup',
      Subscription: 'subscribe_now',
      ChargeActivity: 'profile_transaction',
      RFID: RFIDLinkingConfig,
      Offers: 'offers',
    },
  },
};

const openInitialUrl = async () => {
  const initialUrl: any = await Linking.getInitialURL();
  if (initialUrl) {
    Linking.openURL(initialUrl);
  }
};

// Main application, to be wrapped by providers
const AppScreens = () => {
  const { fullOutageEnabled, fullOutage } = useOutage();
  const { userInfo } = useAppSettings();
  const isSupplierError = getRfidError(userInfo.tagIds);
  // Handle on app launch handlers here
  const checkForRfidErrors = useCallback(() => {
    if (isSupplierError) {
      navigate('rfidError');
    }
  }, [isSupplierError]);

  useEffect(() => {
    checkForRfidErrors();
  }, [isSupplierError, checkForRfidErrors]);

  useEffect(() => {
    applyBackHandleListener();
    openInitialUrl();

    return () => {
      removeBackHandleListener();
    };
  }, []);

  const isUK = userInfo?.userCountry === SupportedCountries.UK;
  const isEmspUser = userInfo?.userType === UserTypes.PAYG_WALLET;
  const isUber = userInfo?.partnerType === SupportedPartner.UBER;

  const validTags: TagStatus[] = [
    TagStatus.CUSTOMER_REQUESTED,
    TagStatus.SUPPLIER_REQUESTED,
    TagStatus.SUPPLIER_ACCEPTED,
    TagStatus.ACTIVE,
    TagStatus.NO_CREDIT,
    TagStatus.PENDING_TERMINATION,
  ];

  const hasValidRfidCard = userInfo?.tagIds?.some(
    tag =>
      tag.tagTypeName === 'physical' &&
      tag.tagNotes?.toUpperCase() === 'PHYSICAL-RFID' &&
      tag.tagStatus &&
      validTags.includes(tag.tagStatus as TagStatus),
  );

  useEffect(() => {
    (async () => {
      if (!isUK || !isEmspUser || !isUber || hasValidRfidCard) {
        return;
      }

      // wait for login navigations to happen, so we navigate last
      await wait(500);

      // Navigate complete Setup as blocking screen when user never had an RFID card OR does not have a valid one
      navigate('PartnerDriver', {
        screen: ScreenName.CompleteSetup,
        params: { userJourney: 'uber-microsite' },
      });
    })();
  }, [isUK, isEmspUser, isUber, hasValidRfidCard]);

  if (fullOutageEnabled) {
    analyticsEvent(OutageAnalyticsEventOpenFullOutageScreen());
    return <Outage heading={fullOutage?.heading} body={fullOutage?.body} />;
  }

  return <Screens />;
};

const AppProviders = () => {
  const {
    onboardingStatus: { country, account },
  } = useOnboarding();

  return (
    <ConfigProvider country={country as SupportedCountries} account={account}>
      <ApolloGatewayProvider>
        <ConnectivityProvider>
          <ProfileAppProvider>
            <MigrationContextProvider>
              <WalletProvider>
                <GuestPaymentProvider>
                  <ChargeStateProvider>
                    <ReturnNavigationProvider>
                      <NavigationContainer linking={linking} ref={navigation}>
                        <S.GestureHandlerRootView>
                          <SafeAreaProvider>
                            <OutageProvider>
                              <FavouritesProvider>
                                <LaunchHandler />
                                <AnalyticsMiddleware />
                                <UberProMiddleware />
                                <EmailUpdateMiddleware />
                                <PausedSubscriptionMiddleware />
                                <UberPartnerMiddleware />
                                <OffersMiddleware />
                                <AppScreens />
                              </FavouritesProvider>
                            </OutageProvider>
                          </SafeAreaProvider>
                        </S.GestureHandlerRootView>
                      </NavigationContainer>
                    </ReturnNavigationProvider>
                  </ChargeStateProvider>
                </GuestPaymentProvider>
              </WalletProvider>
            </MigrationContextProvider>
          </ProfileAppProvider>
        </ConnectivityProvider>
      </ApolloGatewayProvider>
    </ConfigProvider>
  );
};

const App = () => {
  return (
    <MigrationContextProvider>
      <ThemeProvider>
        <LanguageProvider>
          <HostAppTranslationsProvider>
            <AuthProvider>
              <OnboardingProvider>
                <AppProviders />
              </OnboardingProvider>
            </AuthProvider>
          </HostAppTranslationsProvider>
        </LanguageProvider>
      </ThemeProvider>
    </MigrationContextProvider>
  );
};

export default App;

LogBox.ignoreLogs([
  // Cardinal SDK issue
  'Module BpCardinalSdk requires main queue setup',
]);
