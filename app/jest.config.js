const ignore =
  '../node_modules/(?!(@react-native|react-native|react-native-config|react-navigation|react-native-status-bar-height|@react-navigation/.*|@react-navigation/native|@react-navigation/stack|@react-native-firebase/analytics|@react-native-firebase/app|react-native-gesture-handler|react-native-geolocation-service/js|react-native-iphone-x-helper|@bp/*|@bp/charge-mfe|@bp/mobile-mfe-ui-components|react-native-modal|react-native-animatable|react-native-reanimated|react-native-redash|react-native-progress|react-native-safe-area-context|react-native-circular-progress|react-native-reanimated-carousel|react-native-email-link|uuid)/)';

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  transformIgnorePatterns: [ignore],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results/jest' }],
  ],
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
  collectCoverage: true,
  coverageThreshold: {
    global: {
      statements: 70,
      branches: 55,
      functions: 54,
      lines: 71,
    },
  },
  setupFiles: ['./jest.setUp.js'],
  testPathIgnorePatterns: ['detox'],
  moduleNameMapper: {
    '\\.(jpg|ico|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
    '^react-native$': '<rootDir>/../node_modules/react-native',
  },
  modulePaths: [
    '../node_modules',
    '../node_modules/@bp/pulse-mobile-sdk/node_modules',
  ],
};
